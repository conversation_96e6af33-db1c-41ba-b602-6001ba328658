import torch
import torch.nn.functional as F
from torchvision import transforms
from torchvision.datasets import MNIST
from torch.utils.data import DataLoader
import onn

def test_model():
    # 创建模型
    model = onn.Net()
    
    # 创建测试数据
    transform = transforms.Compose([transforms.ToTensor()])
    test_dataset = MNIST("./datasets", train=False, transform=transform, download=False)
    test_dataloader = DataLoader(dataset=test_dataset, batch_size=4, shuffle=False)
    
    # 获取一个batch的数据
    for data_batch in test_dataloader:
        images = data_batch[0]  # (4, 1, 28, 28)
        labels = data_batch[1]  # (4,)
        
        print(f"Original images shape: {images.shape}")
        print(f"Original labels shape: {labels.shape}")
        print(f"Labels: {labels}")
        
        # Padding
        images = F.pad(images, pad=(86, 86, 86, 86))  # (4, 1, 200, 200)
        print(f"After padding: {images.shape}")
        
        # 转换为复数表示
        images = torch.cat((images.squeeze(1).unsqueeze(-1),
                          torch.zeros_like(images.squeeze(1).unsqueeze(-1))), dim=-1)
        print(f"After complex conversion: {images.shape}")
        
        # 前向传播
        with torch.no_grad():
            outputs = model(images)
            print(f"Model outputs shape: {outputs.shape}")
            print(f"Model outputs: {outputs}")
            print(f"Predicted classes: {torch.argmax(outputs, dim=1)}")
        
        break

if __name__ == "__main__":
    test_model()
