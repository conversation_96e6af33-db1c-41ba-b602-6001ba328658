import os
import csv
import random
import pathlib
import argparse
import numpy as np
from tqdm import tqdm

import torch
import torchvision
import torch.nn.functional as F
from torchvision import transforms
from torch.utils.data import DataLoader
from torchvision.datasets import MNIST

MNIST.resources = [
    ('https://ossci-datasets.s3.amazonaws.com/mnist/train-images-idx3-ubyte.gz', 'f68b3c2dcbeaaa9fbdd348bbdeb94873'),
    ('https://ossci-datasets.s3.amazonaws.com/mnist/train-labels-idx1-ubyte.gz', 'd53e105ee54ea40749a09fcbcd1e9432'),
    ('https://ossci-datasets.s3.amazonaws.com/mnist/t10k-images-idx3-ubyte.gz', '9fb629c4189551a2d022fa330f9573f3'),
    ('https://ossci-datasets.s3.amazonaws.com/mnist/t10k-labels-idx1-ubyte.gz', 'ec29112dd5afa0611ce80d1b7f02629c')
]

import onn


#os.environ["CUDA_VISIBLE_DEVICES"] = '8'


def main(args):
    import torch

    # 1. 定义设备（自动选择GPU或CPU）
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 3. 删除冲突的环境变量（如果有）
    if 'CUDA_VISIBLE_DEVICES' in os.environ:
        del os.environ['CUDA_VISIBLE_DEVICES']  # 避免干扰设备选择
        
    if not os.path.exists(args.model_save_path):
        os.mkdir(args.model_save_path)

    # transform = transforms.Compose([transforms.Resize(size=(200, 200)), transforms.ToTensor()])
    transform = transforms.Compose([transforms.ToTensor()])
    train_dataset = torchvision.datasets.MNIST("./datasets", train=True, transform=transform, download=False)
    val_dataset = torchvision.datasets.MNIST("./datasets", train=False, transform=transform, download=False)
    train_dataloader = DataLoader(dataset=train_dataset, batch_size=args.batch_size, num_workers=4, shuffle=True, pin_memory=True)
    val_dataloader = DataLoader(dataset=val_dataset, batch_size=args.batch_size, num_workers=4, shuffle=False, pin_memory=True)

    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    model = onn.Net().to(device)

    if args.whether_load_model:
        model.load_state_dict(torch.load(args.model_save_path + str(args.start_epoch) + args.model_name))
        print('Model : "' + args.model_save_path + str(args.start_epoch) + args.model_name + '" loaded.')
    else:
        # 创建新的CSV文件记录训练结果
        with open(args.result_record_path, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(
                ['Epoch', 'Train_Loss', "Train_Acc", 'Val_Loss', "Val_Acc", "LR"])

    criterion = torch.nn.CrossEntropyLoss().to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)

    for epoch in range(args.start_epoch + 1, args.start_epoch + 1 + args.num_epochs):

        log = [epoch]

        model.train()

        train_len = 0.0
        train_running_counter = 0.0
        train_running_loss = 0.0

        tk0 = tqdm(train_dataloader, ncols=100, total=int(len(train_dataloader)))
        for train_data_batch in tk0:

            train_images = train_data_batch[0].to(device)          # (batch, 1, 28, 28) float32
            train_labels = train_data_batch[1].to(device)          # (batch,) int64
            train_images = F.pad(train_images, pad=(86, 86, 86, 86))  # pad to (batch, 1, 200, 200)

            # 将图像转换为复数表示：(batch, 1, 200, 200) -> (batch, 200, 200, 2)
            train_images = torch.cat((train_images.squeeze(1).unsqueeze(-1),
                                    torch.zeros_like(train_images.squeeze(1).unsqueeze(-1))), dim=-1)

            train_outputs = model(train_images)

            train_loss_ = criterion(train_outputs, train_labels)
            train_counter_ = torch.eq(train_labels, torch.argmax(train_outputs, dim=1)).float().sum()

            optimizer.zero_grad()
            train_loss_.backward()
            optimizer.step()

            train_len += len(train_labels)
            train_running_loss += train_loss_.item()
            train_running_counter += train_counter_

            train_loss = train_running_loss / train_len
            train_accuracy = train_running_counter / train_len

            tk0.set_description_str('Epoch {}/{} : Training'.format(epoch, args.start_epoch + 1 + args.num_epochs - 1))
            tk0.set_postfix({'Train_Loss': '{:.5f}'.format(train_loss), 'Train_Accuracy': '{:.5f}'.format(train_accuracy)})

        log.append(train_loss)
        log.append(train_accuracy)

        with torch.no_grad():
            # 验证
            model.eval()

            val_len = 0.0
            val_running_counter = 0.0
            val_running_loss = 0.0

            tk1 = tqdm(val_dataloader, ncols=100, total=int(len(val_dataloader)))
            for val_data_batch in tk1:

                val_images = val_data_batch[0].to(device)  # (batch, 1, 28, 28) float32
                val_labels = val_data_batch[1].to(device)  # (batch,) int64
                val_images = F.pad(val_images, pad=(86, 86, 86, 86))  # pad to (batch, 1, 200, 200)

                # 将图像转换为复数表示：(batch, 1, 200, 200) -> (batch, 200, 200, 2)
                val_images = torch.cat((val_images.squeeze(1).unsqueeze(-1),
                                      torch.zeros_like(val_images.squeeze(1).unsqueeze(-1))), dim=-1)

                val_outputs = model(val_images)

                val_loss_ = criterion(val_outputs, val_labels)
                val_counter_ = torch.eq(val_labels, torch.argmax(val_outputs, dim=1)).float().sum()

                val_len += len(val_labels)
                val_running_loss += val_loss_.item()
                val_running_counter += val_counter_

                val_loss = val_running_loss / val_len
                val_accuracy = val_running_counter / val_len

                tk1.set_description_str('Epoch {}/{} : Validating'.format(epoch, args.start_epoch + 1 + args.num_epochs - 1))
                tk1.set_postfix({'Val_Loss': '{:.5f}'.format(val_loss), 'Val_Accuracy': '{:.5f}'.format(val_accuracy)})

            log.append(val_loss)
            log.append(val_accuracy)
            log.append(optimizer.param_groups[0]['lr'])  # 记录当前学习率

        torch.save(model.state_dict(), (args.model_save_path + str(epoch) + args.model_name))
        print('Model : "' + args.model_save_path + str(epoch) + args.model_name + '" saved.')

        with open(args.result_record_path, 'a', newline="") as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(log)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    # 训练参数
    parser.add_argument('--batch-size', type=int, default=1024)
    parser.add_argument('--num-epochs', type=int, default=400)
    parser.add_argument('--seed', type=int, default=42)
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    parser.add_argument('--whether-load-model', type=bool, default=False, help="是否加载模型继续训练")
    parser.add_argument('--start-epoch', type=int, default=0, help='从哪个epoch继续训练')
    # 数据和模型相关
    parser.add_argument('--model-name', type=str, default='_model.pth')
    parser.add_argument('--model-save-path', type=str, default="./saved_model/")
    parser.add_argument('--result-record-path', type=pathlib.Path, default="./result.csv", help="数值结果记录路径")

    torch.backends.cudnn.benchmark = True
    args_ = parser.parse_args()
    random.seed(args_.seed)
    np.random.seed(args_.seed)
    torch.manual_seed(args_.seed)
    main(args_)